// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		22FD76F52DE5ED3E00FB5EE1 /* NeuroLoopApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22FD76F42DE5ED3E00FB5EE1 /* NeuroLoopApp.swift */; };
		22FD76F92DE5ED3F00FB5EE1 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 22FD76F82DE5ED3F00FB5EE1 /* Assets.xcassets */; };
		22FD76FC2DE5ED3F00FB5EE1 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 22FD76FB2DE5ED3F00FB5EE1 /* Preview Assets.xcassets */; };
		22FD77202DE5ED3E00FB5EE1 /* NeuroLoopCore in Frameworks */ = {isa = PBXBuildFile; productRef = 22FD771F2DE5ED3E00FB5EE1 /* NeuroLoopCore */; };
		22FD77222DE5ED3E00FB5EE1 /* NeuroLoopUI in Frameworks */ = {isa = PBXBuildFile; productRef = 22FD77212DE5ED3E00FB5EE1 /* NeuroLoopUI */; };
		22FD77242DE5ED3E00FB5EE1 /* NeuroLoopTypes in Frameworks */ = {isa = PBXBuildFile; productRef = 22FD77232DE5ED3E00FB5EE1 /* NeuroLoopTypes */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		22FD76F12DE5ED3E00FB5EE1 /* NeuroLoopApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NeuroLoopApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		22FD76F42DE5ED3E00FB5EE1 /* NeuroLoopApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NeuroLoopApp.swift; sourceTree = "<group>"; };
		22FD76F82DE5ED3F00FB5EE1 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		22FD76FB2DE5ED3F00FB5EE1 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		22FD76FD2DE5ED4000FB5EE1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		22FD76EE2DE5ED3E00FB5EE1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22FD77202DE5ED3E00FB5EE1 /* NeuroLoopCore in Frameworks */,
				22FD77222DE5ED3E00FB5EE1 /* NeuroLoopUI in Frameworks */,
				22FD77242DE5ED3E00FB5EE1 /* NeuroLoopTypes in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		22FD76E82DE5ED3E00FB5EE1 = {
			isa = PBXGroup;
			children = (
				22FD76F32DE5ED3E00FB5EE1 /* NeuroLoopApp */,
				22FD76F22DE5ED3E00FB5EE1 /* Products */,
			);
			sourceTree = "<group>";
		};
		22FD76F22DE5ED3E00FB5EE1 /* Products */ = {
			isa = PBXGroup;
			children = (
				22FD76F12DE5ED3E00FB5EE1 /* NeuroLoopApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22FD76F32DE5ED3E00FB5EE1 /* NeuroLoopApp */ = {
			isa = PBXGroup;
			children = (
				22FD76F42DE5ED3E00FB5EE1 /* NeuroLoopApp.swift */,
				22FD76FD2DE5ED4000FB5EE1 /* Info.plist */,
				22FD76F82DE5ED3F00FB5EE1 /* Assets.xcassets */,
				22FD76FA2DE5ED3F00FB5EE1 /* Preview Content */,
			);
			path = NeuroLoopApp;
			sourceTree = "<group>";
		};
		22FD76FA2DE5ED3F00FB5EE1 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				22FD76FB2DE5ED3F00FB5EE1 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		22FD76F02DE5ED3E00FB5EE1 /* NeuroLoopApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22FD76FF2DE5ED3F00FB5EE1 /* Build configuration list for PBXNativeTarget "NeuroLoopApp" */;
			buildPhases = (
				22FD76ED2DE5ED3E00FB5EE1 /* Sources */,
				22FD76EE2DE5ED3E00FB5EE1 /* Frameworks */,
				22FD76EF2DE5ED3E00FB5EE1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NeuroLoopApp;
			packageProductDependencies = (
				22FD771F2DE5ED3E00FB5EE1 /* NeuroLoopCore */,
				22FD77212DE5ED3E00FB5EE1 /* NeuroLoopUI */,
				22FD77232DE5ED3E00FB5EE1 /* NeuroLoopTypes */,
			);
			productName = NeuroLoopApp;
			productReference = 22FD76F12DE5ED3E00FB5EE1 /* NeuroLoopApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		22FD76E92DE5ED3E00FB5EE1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					22FD76F02DE5ED3E00FB5EE1 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 22FD76EC2DE5ED3E00FB5EE1 /* Build configuration list for PBXProject "NeuroLoopApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 22FD76E82DE5ED3E00FB5EE1;
			packageReferences = (
				22FD771E2DE5ED3E00FB5EE1 /* XCLocalSwiftPackageReference "." */,
			);
			productRefGroup = 22FD76F22DE5ED3E00FB5EE1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				22FD76F02DE5ED3E00FB5EE1 /* NeuroLoopApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		22FD76EF2DE5ED3E00FB5EE1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22FD76FC2DE5ED3F00FB5EE1 /* Preview Assets.xcassets in Resources */,
				22FD76F92DE5ED3F00FB5EE1 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22FD76ED2DE5ED3E00FB5EE1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22FD76F52DE5ED3E00FB5EE1 /* NeuroLoopApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		22FD76FD2DE5ED3F00FB5EE1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		22FD76FE2DE5ED3F00FB5EE1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		22FD77002DE5ED3F00FB5EE1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"NeuroLoopApp/Preview Content\"";
				DEVELOPMENT_TEAM = 48A5949N5D;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = NeuroLoopApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoopApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		22FD77012DE5ED3F00FB5EE1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"NeuroLoopApp/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = NeuroLoopApp/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.neuroloop.NeuroLoopApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22FD76EC2DE5ED3E00FB5EE1 /* Build configuration list for PBXProject "NeuroLoopApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22FD76FD2DE5ED3F00FB5EE1 /* Debug */,
				22FD76FE2DE5ED3F00FB5EE1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22FD76FF2DE5ED3F00FB5EE1 /* Build configuration list for PBXNativeTarget "NeuroLoopApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22FD77002DE5ED3F00FB5EE1 /* Debug */,
				22FD77012DE5ED3F00FB5EE1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		22FD771E2DE5ED3E00FB5EE1 /* XCLocalSwiftPackageReference "." */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = .;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		22FD771F2DE5ED3E00FB5EE1 /* NeuroLoopCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 22FD771E2DE5ED3E00FB5EE1 /* XCLocalSwiftPackageReference "." */;
			productName = NeuroLoopCore;
		};
		22FD77212DE5ED3E00FB5EE1 /* NeuroLoopUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 22FD771E2DE5ED3E00FB5EE1 /* XCLocalSwiftPackageReference "." */;
			productName = NeuroLoopUI;
		};
		22FD77232DE5ED3E00FB5EE1 /* NeuroLoopTypes */ = {
			isa = XCSwiftPackageProductDependency;
			package = 22FD771E2DE5ED3E00FB5EE1 /* XCLocalSwiftPackageReference "." */;
			productName = NeuroLoopTypes;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 22FD76E92DE5ED3E00FB5EE1 /* Project object */;
}
