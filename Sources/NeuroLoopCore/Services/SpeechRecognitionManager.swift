import AVFoundation
import Speech
import Foundation

/// Optimized speech recognition manager with pre-initialization and background setup
@MainActor
public final class SpeechRecognitionManager: ObservableObject, @unchecked Sendable {
    // MARK: - Shared Instance
    
    public static let shared = SpeechRecognitionManager()
    
    // MARK: - Published Properties
    
    @Published public private(set) var isReady = false
    @Published public private(set) var isInitializing = false
    @Published public private(set) var authorizationStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    @Published public private(set) var isAvailable = false
    
    // MARK: - Private Properties
    
    private var speechRecognizer: SFSpeechRecognizer?
    private var audioEngine: AVAudioEngine?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    
    private var preInitializationTask: Task<Void, Never>?
    
    // MARK: - Initialization
    
    private init() {
        print("[SpeechRecognitionManager] Initializing speech recognition manager")
        setupInitialState()
    }
    
    private func setupInitialState() {
        authorizationStatus = SFSpeechRecognizer.authorizationStatus()
        
        // Check if speech recognition is available
        let preferredLocale = Locale.current
        let localeIdentifier = preferredLocale.language.languageCode?.identifier ?? "en"
        let recognizerLocale = Locale(identifier: "\(localeIdentifier)-US")
        
        speechRecognizer = SFSpeechRecognizer(locale: recognizerLocale)
        isAvailable = speechRecognizer?.isAvailable ?? false
        
        print("[SpeechRecognitionManager] Initial setup - Available: \(isAvailable), Auth: \(authorizationStatus)")
    }
    
    // MARK: - Pre-initialization
    
    /// Pre-warm speech recognition components in background
    public func preInitialize() async {
        guard preInitializationTask == nil else {
            await preInitializationTask?.value
            return
        }
        
        preInitializationTask = Task { @MainActor in
            await performPreInitialization()
        }
        
        await preInitializationTask?.value
    }
    
    private func performPreInitialization() async {
        guard !isReady else { return }

        isInitializing = true

        print("[SpeechRecognitionManager] Starting pre-initialization...")

        // 1. Setup audio session in background
        await setupAudioSession()

        // 2. Pre-warm audio engine
        await setupAudioEngine()

        // 3. DON'T request permissions during initialization - only when actually needed
        // This prevents crashes during app startup when permissions haven't been granted
        print("[SpeechRecognitionManager] Skipping permission requests during initialization")

        // 4. Validate speech recognizer
        await validateSpeechRecognizer()

        isReady = true
        print("[SpeechRecognitionManager] Pre-initialization completed successfully")

        isInitializing = false
    }
    
    // MARK: - Audio Session Setup

    private func setupAudioSession() async {
        await withCheckedContinuation { continuation in
            Task {
                do {
                    #if os(iOS)
                    let audioSession = AVAudioSession.sharedInstance()

                    // Configure audio session for recording but don't activate yet
                    // CRITICAL FIX: Use consistent audio session configuration
                    // Match the SpeakAffirmationViewModel configuration to avoid conflicts
                    try audioSession.setCategory(
                        .playAndRecord,
                        mode: .spokenAudio,
                        options: [.defaultToSpeaker, .allowBluetooth]
                    )
                    #endif

                    // Don't activate the session during initialization - wait until we have permissions
                    // This prevents crashes when microphone permissions haven't been granted
                    print("[SpeechRecognitionManager] Audio session configured (not activated yet)")
                    continuation.resume()

                } catch {
                    print("[SpeechRecognitionManager] Audio session setup failed: \(error)")
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Audio Engine Setup
    
    private func setupAudioEngine() async {
        // DON'T create AVAudioEngine during initialization
        // Creating AVAudioEngine might trigger internal initialization that requires permissions
        // We'll create it only when we actually start speech recognition

        print("[SpeechRecognitionManager] Audio engine setup deferred until actual use")
    }
    
    // MARK: - Permission Management
    
    private func requestPermissionsIfNeeded() async {
        // Request speech recognition permission if not determined
        if authorizationStatus == .notDetermined {
            print("[SpeechRecognitionManager] Requesting speech recognition permission...")
            
            let granted = await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { status in
                    continuation.resume(returning: status == .authorized)
                }
            }
            
            await MainActor.run {
                authorizationStatus = SFSpeechRecognizer.authorizationStatus()
            }
            
            print("[SpeechRecognitionManager] Speech permission result: \(granted)")
        }
        
        // Request microphone permission using the correct API
        let microphonePermission: Bool
        if #available(iOS 17.0, *) {
            microphonePermission = await AVAudioApplication.requestRecordPermission()
        } else {
            microphonePermission = await withCheckedContinuation { continuation in
                #if os(iOS)
                AVAudioSession.sharedInstance().requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
                #else
                // On macOS, assume permission is granted for now
                continuation.resume(returning: true)
                #endif
            }
        }
        print("[SpeechRecognitionManager] Microphone permission: \(microphonePermission)")
    }
    
    private func validateSpeechRecognizer() async {
        guard let recognizer = speechRecognizer else {
            print("[SpeechRecognitionManager] Speech recognizer not available")
            return
        }
        
        isAvailable = recognizer.isAvailable
        
        if !isAvailable {
            print("[SpeechRecognitionManager] Speech recognizer not available for current locale")
        } else {
            print("[SpeechRecognitionManager] Speech recognizer validated successfully")
        }
    }
    
    // MARK: - Quick Start Recognition
    
    /// Start speech recognition with minimal setup time (uses pre-initialized components)
    public func startQuickRecognition(
        contextualStrings: [String] = [],
        partialResultsHandler: @escaping (String) -> Void,
        finalResultHandler: @escaping (String, Bool) -> Void,
        errorHandler: @escaping (Error) -> Void
    ) async {
        guard isReady else {
            print("[SpeechRecognitionManager] Not ready - call preInitialize() first")
            errorHandler(SpeechRecognitionError.notReady)
            return
        }

        // Request permissions when actually needed (not during initialization)
        if authorizationStatus != .authorized {
            print("[SpeechRecognitionManager] Requesting permissions before starting recognition")
            await requestPermissionsIfNeeded()
        }

        guard authorizationStatus == .authorized else {
            print("[SpeechRecognitionManager] Speech recognition not authorized")
            errorHandler(SpeechRecognitionError.notAuthorized)
            return
        }
        
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("[SpeechRecognitionManager] Speech recognizer not available")
            errorHandler(SpeechRecognitionError.recognizerUnavailable)
            return
        }
        
        // Create audio engine when actually needed (not during initialization)
        let audioEngine = AVAudioEngine()
        self.audioEngine = audioEngine
        
        do {
            // Stop any existing recognition
            await stopRecognition()
            
            // Create recognition request
            let request = SFSpeechAudioBufferRecognitionRequest()
            request.shouldReportPartialResults = true
            request.contextualStrings = contextualStrings
            
            recognitionRequest = request
            
            // Setup audio tap
            let inputNode = audioEngine.inputNode
            let recordingFormat = inputNode.outputFormat(forBus: 0)
            
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                request.append(buffer)
            }
            
            // Start audio engine
            try audioEngine.start()
            
            // Start recognition task
            recognitionTask = speechRecognizer.recognitionTask(with: request) { result, error in
                Task { @MainActor in
                    if let result = result {
                        let recognizedText = result.bestTranscription.formattedString
                        partialResultsHandler(recognizedText)

                        if result.isFinal {
                            finalResultHandler(recognizedText, true)
                        }
                    }

                    if let error = error {
                        // Filter out transient "no speech detected" errors that happen immediately after starting
                        let errorMessage = error.localizedDescription.lowercased()
                        if errorMessage.contains("no speech") || errorMessage.contains("speech not detected") {
                            print("[SpeechRecognitionManager] Ignoring transient 'no speech detected' error - recognition continues")
                            // Don't call errorHandler for these transient errors
                            return
                        }

                        // Only report significant errors
                        print("[SpeechRecognitionManager] Speech recognition error: \(error)")
                        errorHandler(error)
                    }
                }
            }
            
            print("[SpeechRecognitionManager] Quick recognition started successfully")
            
        } catch {
            print("[SpeechRecognitionManager] Failed to start quick recognition: \(error)")
            errorHandler(error)
        }
    }
    
    /// Stop speech recognition
    public func stopRecognition() async {
        recognitionTask?.cancel()
        recognitionTask = nil
        
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        
        print("[SpeechRecognitionManager] Recognition stopped")
    }
    
    // MARK: - Cleanup
    
    public func cleanup() async {
        await stopRecognition()
        
        audioEngine = nil
        speechRecognizer = nil
        
        isReady = false
        
        print("[SpeechRecognitionManager] Cleanup completed")
    }
}

// MARK: - Error Types

public enum SpeechRecognitionError: Error, LocalizedError {
    case notReady
    case notAuthorized
    case recognizerUnavailable
    case audioEngineNotReady
    case setupFailed(Error)
    
    public var errorDescription: String? {
        switch self {
        case .notReady:
            return "Speech recognition not ready. Call preInitialize() first."
        case .notAuthorized:
            return "Speech recognition not authorized."
        case .recognizerUnavailable:
            return "Speech recognizer not available."
        case .audioEngineNotReady:
            return "Audio engine not ready."
        case .setupFailed(let error):
            return "Speech recognition setup failed: \(error.localizedDescription)"
        }
    }
}
