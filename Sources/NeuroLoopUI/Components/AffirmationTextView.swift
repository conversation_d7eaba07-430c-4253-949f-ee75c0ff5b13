import NeuroLoopCore
import NeuroLoopTypes
import SwiftUI

/// A view for displaying affirmation text with optional "Repeat Exactly" button
@available(iOS 17.0, macOS 14.0, *)
public struct AffirmationTextView: View {
    // MARK: - Properties

    let text: String
    var showRepeatExactly: Bool = false
    var onRepeatExactly: (() -> Void)? = nil
    var isRecording: Bool = false  // We keep this parameter for compatibility but don't use it to show a red dot
    @State private var showingTips = false

    // Animation properties
    @State private var animationTrigger = false

    // MARK: - Initialization

    public init(
        text: String,
        showRepeatExactly: Bool = false,
        onRepeatExactly: (() -> Void)? = nil,
        isRecording: Bool = false
    ) {
        self.text = text
        self.showRepeatExactly = showRepeatExactly
        self.onRepeatExactly = onRepeatExactly
        self.isRecording = isRecording
    }

    // MARK: - Body

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            if showRepeatExactly {
                HStack {
                    Button(action: {
                        onRepeatExactly?()
                    }) {
                        Label("Repeat Exactly", systemImage: "speaker.wave.2.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.white.opacity(0.2))
                            .cornerRadius(8)
                    }
                    .accessibilityLabel("Repeat Exactly")
                    .accessibilityHint("Tap to hear the affirmation spoken")

                    Spacer()

                    Button(action: {
                        showingTips.toggle()
                    }) {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .padding(8)
                            .background(Color.white.opacity(0.2))
                            .clipShape(Circle())
                    }
                    .accessibilityLabel("Tips")
                    .accessibilityHint("Tap to show tips for effective affirmations")
                }
            }

            HStack {
                // Left quotation mark
                Text(
                    """
                    "
                    """
                )
                .font(.system(size: 40, weight: .bold, design: .serif))
                .foregroundColor(.white.opacity(0.5))
                .padding(.leading, -10)
                .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)

                Spacer()
            }

            // Affirmation text
            Text(text)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineSpacing(8)
                .padding(.horizontal)
                .shadow(color: Color.black.opacity(0.2), radius: 1, x: 0, y: 1)
            // Removed animation and scaling effect to avoid duplicate recording indicators

            HStack {
                Spacer()

                // Right quotation mark
                Text(
                    """
                    "
                    """
                )
                .font(.system(size: 40, weight: .bold, design: .serif))
                .foregroundColor(.white.opacity(0.5))
                .padding(.trailing, -10)
                .shadow(color: Color.black.opacity(0.2), radius: 2, x: 1, y: 1)
            }
        }
        .sheet(isPresented: $showingTips) {
            TipsView()
        }
    }
}

// MARK: - Tips View

@available(iOS 17.0, macOS 14.0, *)
private struct TipsView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Tips for Effective Affirmations")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.bottom, 10)

                    TipItem(
                        icon: "text.quote",
                        title: "Speak Clearly",
                        description: "Pronounce each word clearly and with conviction."
                    )

                    TipItem(
                        icon: "ear",
                        title: "Listen to Yourself",
                        description:
                            "Pay attention to how your voice sounds. Speak with confidence."
                    )

                    TipItem(
                        icon: "arrow.clockwise",
                        title: "Repeat Exactly",
                        description:
                            "Use the 'Repeat Exactly' button to hear the correct pronunciation."
                    )

                    TipItem(
                        icon: "chart.bar.fill",
                        title: "Track Progress",
                        description: "Complete your daily repetitions to build a streak."
                    )
                }
                .padding()
            }
            .navigationTitle("Tips")
            #if os(iOS)
                .navigationBarTitleDisplayMode(.inline)
            #endif
        }
    }
}

// MARK: - Tip Item

@available(iOS 17.0, macOS 14.0, *)
private struct TipItem: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
    @available(iOS 17.0, macOS 14.0, *)
    struct AffirmationTextView_Previews: PreviewProvider {
        static var previews: some View {
            ZStack {
                Color.blue
                    .ignoresSafeArea()

                AffirmationTextView(
                    text: "I am confident and capable in everything I do",
                    showRepeatExactly: true,
                    onRepeatExactly: {},
                    isRecording: true
                )
                .padding()
            }
        }
    }
#endif
